import ChatWootWidget from '@chatwoot/react-native-widget';
import React from 'react';
import { SafeAreaView, StyleSheet } from 'react-native';

export default function ChatWrapper() {
  const user = {
    identifier: '<EMAIL>',
    name: '<PERSON>',
    avatar_url: '',
    email: '<EMAIL>',
    identifier_hash: '',
  };

  const customAttributes = { accountId: 1, pricingPlan: 'paid', status: 'active' };
  const websiteToken = 'YOUR_WEBSITE_TOKEN';
  const baseUrl = 'https://app.chatwoot.com'; // hoặc domain chatwoot tự host
  const locale = 'en';
  const colorScheme = 'dark';

  return (
    <SafeAreaView style={styles.container}>
      <ChatWootWidget
        websiteToken={websiteToken}
        locale={locale}
        baseUrl={baseUrl}
        closeModal={() => {}}
        isModalVisible={true} // luôn hiển thị
        user={user}
        customAttributes={customAttributes}
        colorScheme={colorScheme}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000', // để khớp giao diện dark
  },
});
